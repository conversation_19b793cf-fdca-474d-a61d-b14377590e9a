<!--
  分片上传管理页面
  管理员可以查看活跃的上传会话并进行管理
-->

<template>
  <div class="chunk-upload-management">
    <div class="page-header">
      <h1>分片上传管理</h1>
      <p>管理系统中的文件分片上传会话</p>
    </div>

    <!-- 操作栏 -->
    <div class="action-bar">
      <el-button type="primary" :loading="loading" @click="refreshSessions">
        <el-icon><refresh /></el-icon>
        刷新
      </el-button>
      <el-button
        type="warning"
        :loading="cleanupLoading"
        @click="cleanupExpiredSessions"
      >
        <el-icon><delete /></el-icon>
        清理过期会话
      </el-button>
    </div>

    <!-- 统计信息 -->
    <div class="stats-cards">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ totalSessions }}</div>
              <div class="stat-label">活跃会话</div>
            </div>
            <el-icon class="stat-icon active"><upload /></el-icon>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ completedSessions }}</div>
              <div class="stat-label">已完成</div>
            </div>
            <el-icon class="stat-icon success"><check /></el-icon>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ formatFileSize(totalDataSize) }}</div>
              <div class="stat-label">总数据量</div>
            </div>
            <el-icon class="stat-icon info"><folder /></el-icon>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 会话列表 -->
    <el-card class="sessions-card">
      <template #header>
        <div class="card-header">
          <span>活跃上传会话</span>
          <el-text type="info">共 {{ sessions.length }} 个会话</el-text>
        </div>
      </template>

      <el-table
        v-loading="loading"
        :data="sessions"
        empty-text="暂无活跃的上传会话"
        stripe
      >
        <el-table-column prop="uploadId" label="会话ID" width="180">
          <template #default="{ row }">
            <el-text class="upload-id">{{ row.uploadId }}</el-text>
          </template>
        </el-table-column>

        <el-table-column prop="fileName" label="文件名" min-width="200">
          <template #default="{ row }">
            <div class="file-info">
              <el-icon class="file-icon"><document /></el-icon>
              <div class="file-details">
                <div class="file-name">{{ row.fileName }}</div>
                <div class="file-size">{{ formatFileSize(row.fileSize) }}</div>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="progress" label="进度" width="150">
          <template #default="{ row }">
            <div class="progress-cell">
              <el-progress
                :percentage="parseFloat(row.progress)"
                :stroke-width="8"
                :status="getProgressStatus(row.progress)"
                :show-text="false"
              />
              <span class="progress-text">{{ row.progress }}%</span>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="createdAt" label="创建时间" width="180">
          <template #default="{ row }">
            <div class="time-cell">
              <el-icon><clock /></el-icon>
              <span>{{ formatDateTime(row.createdAt) }}</span>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="lastActivity" label="最后活动" width="180">
          <template #default="{ row }">
            <div class="time-cell">
              <el-icon><clock /></el-icon>
              <span>{{ formatDateTime(row.lastActivity) }}</span>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.progress)" size="small">
              {{ getStatusText(row.progress) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="120" fixed="right">
          <template #default="{ row }">
            <div class="action-buttons">
              <el-button
                type="primary"
                size="small"
                @click="viewSessionDetails(row)"
              >
                详情
              </el-button>
              <el-button type="danger" size="small" @click="cancelSession(row)">
                取消
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 会话详情弹窗 -->
    <el-dialog v-model="detailsDialogVisible" title="会话详情" width="600px">
      <div v-if="selectedSession" class="session-details">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="会话ID">
            <el-text class="upload-id">{{ selectedSession.uploadId }}</el-text>
          </el-descriptions-item>
          <el-descriptions-item label="文件名">
            {{ selectedSession.fileName }}
          </el-descriptions-item>
          <el-descriptions-item label="文件大小">
            {{ formatFileSize(selectedSession.fileSize) }}
          </el-descriptions-item>
          <el-descriptions-item label="上传进度">
            <el-progress
              :percentage="parseFloat(selectedSession.progress)"
              :stroke-width="8"
              :status="getProgressStatus(selectedSession.progress)"
            />
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">
            {{ formatDateTime(selectedSession.createdAt) }}
          </el-descriptions-item>
          <el-descriptions-item label="最后活动">
            {{ formatDateTime(selectedSession.lastActivity) }}
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag
              :type="getStatusType(selectedSession.progress)"
              size="small"
            >
              {{ getStatusText(selectedSession.progress) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="持续时间">
            {{
              getDuration(
                selectedSession.createdAt,
                selectedSession.lastActivity,
              )
            }}
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="detailsDialogVisible = false">关闭</el-button>
          <el-button
            type="danger"
            :loading="cancelLoading"
            @click="cancelSessionFromDetails"
          >
            取消会话
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import {
  Refresh,
  Delete,
  Upload,
  Check,
  Folder,
  Document,
  Clock,
} from "@element-plus/icons-vue";
import chunkUploadClient from "@/api/chunkUpload";

export default {
  name: "ChunkUploadManagement",
  components: {
    Refresh,
    Delete,
    Upload,
    Check,
    Folder,
    Document,
    Clock,
  },
  setup() {
    // 响应式数据
    const sessions = ref([]);
    const loading = ref(false);
    const cleanupLoading = ref(false);
    const cancelLoading = ref(false);
    const detailsDialogVisible = ref(false);
    const selectedSession = ref(null);

    // 计算属性
    const totalSessions = computed(() => sessions.value.length);
    const completedSessions = computed(
      () => sessions.value.filter((s) => parseFloat(s.progress) >= 100).length,
    );
    const totalDataSize = computed(() =>
      sessions.value.reduce((total, s) => total + s.fileSize, 0),
    );

    // 获取活跃会话
    const refreshSessions = async () => {
      try {
        loading.value = true;
        const response = await chunkUploadClient.getActiveSessions();
        sessions.value = response.data || [];
      } catch (error) {
        if (import.meta.env.DEV) {
          console.error("Refresh sessions error:", error);
        }
        ElMessage.error(error.message || "获取会话列表失败");
      } finally {
        loading.value = false;
      }
    };

    // 清理过期会话
    const cleanupExpiredSessions = async () => {
      try {
        await ElMessageBox.confirm(
          "确定要清理所有过期的上传会话吗？",
          "确认清理",
          {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          },
        );

        cleanupLoading.value = true;
        await chunkUploadClient.cleanupExpiredSessions();
        ElMessage.success("过期会话清理成功");

        // 刷新会话列表
        await refreshSessions();
      } catch (error) {
        if (error !== "cancel") {
          if (import.meta.env.DEV) {
            console.error("Cleanup sessions error:", error);
          }
          ElMessage.error(error.message || "清理会话失败");
        }
      } finally {
        cleanupLoading.value = false;
      }
    };

    // 查看会话详情
    const viewSessionDetails = (session) => {
      selectedSession.value = session;
      detailsDialogVisible.value = true;
    };

    // 取消会话
    const cancelSession = async (session) => {
      try {
        await ElMessageBox.confirm(
          `确定要取消会话 "${session.fileName}" 吗？`,
          "确认取消",
          {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          },
        );

        await chunkUploadClient.cancelUpload(session.uploadId);
        ElMessage.success("会话已取消");

        // 刷新会话列表
        await refreshSessions();
      } catch (error) {
        if (error !== "cancel") {
          if (import.meta.env.DEV) {
            console.error("Cancel session error:", error);
          }
          ElMessage.error(error.message || "取消会话失败");
        }
      }
    };

    // 从详情对话框取消会话
    const cancelSessionFromDetails = async () => {
      if (!selectedSession.value) return;

      try {
        cancelLoading.value = true;
        await chunkUploadClient.cancelUpload(selectedSession.value.uploadId);
        ElMessage.success("会话已取消");

        detailsDialogVisible.value = false;
        selectedSession.value = null;

        // 刷新会话列表
        await refreshSessions();
      } catch (error) {
        if (import.meta.env.DEV) {
          console.error("Cancel session error:", error);
        }
        ElMessage.error(error.message || "取消会话失败");
      } finally {
        cancelLoading.value = false;
      }
    };

    // 工具方法
    const formatFileSize = (bytes) => {
      if (bytes === 0) return "0 B";
      const k = 1024;
      const sizes = ["B", "KB", "MB", "GB"];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
    };

    const formatDateTime = (dateString) => {
      const date = new Date(dateString);
      return date.toLocaleString("zh-CN");
    };

    const getProgressStatus = (progress) => {
      const prog = parseFloat(progress);
      if (prog >= 100) return "success";
      if (prog >= 80) return "warning";
      return "active";
    };

    const getStatusType = (progress) => {
      const prog = parseFloat(progress);
      if (prog >= 100) return "success";
      if (prog >= 80) return "warning";
      return "primary";
    };

    const getStatusText = (progress) => {
      const prog = parseFloat(progress);
      if (prog >= 100) return "已完成";
      if (prog >= 80) return "即将完成";
      if (prog > 0) return "上传中";
      return "准备中";
    };

    const getDuration = (startTime, endTime) => {
      const start = new Date(startTime);
      const end = new Date(endTime);
      const duration = end - start;

      const hours = Math.floor(duration / (1000 * 60 * 60));
      const minutes = Math.floor((duration % (1000 * 60 * 60)) / (1000 * 60));
      const seconds = Math.floor((duration % (1000 * 60)) / 1000);

      if (hours > 0) {
        return `${hours}小时${minutes}分钟`;
      } else if (minutes > 0) {
        return `${minutes}分钟${seconds}秒`;
      } else {
        return `${seconds}秒`;
      }
    };

    // 生命周期钩子
    onMounted(() => {
      refreshSessions();
    });

    return {
      // 响应式数据
      sessions,
      loading,
      cleanupLoading,
      cancelLoading,
      detailsDialogVisible,
      selectedSession,

      // 计算属性
      totalSessions,
      completedSessions,
      totalDataSize,

      // 方法
      refreshSessions,
      cleanupExpiredSessions,
      viewSessionDetails,
      cancelSession,
      cancelSessionFromDetails,

      // 工具方法
      formatFileSize,
      formatDateTime,
      getProgressStatus,
      getStatusType,
      getStatusText,
      getDuration,
    };
  },
};
</script>

<style scoped>
.chunk-upload-management {
  padding: 20px;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.action-bar {
  margin-bottom: 20px;
  display: flex;
  gap: 12px;
}

.stats-cards {
  margin-bottom: 20px;
}

.stat-card {
  padding: 0;
}

.stat-card :deep(.el-card__body) {
  padding: 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 28px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #606266;
}

.stat-icon {
  font-size: 32px;
  opacity: 0.8;
}

.stat-icon.active {
  color: #409eff;
}

.stat-icon.success {
  color: #67c23a;
}

.stat-icon.info {
  color: #909399;
}

.sessions-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.upload-id {
  font-family: monospace;
  font-size: 12px;
  color: #606266;
}

.file-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.file-icon {
  color: #409eff;
  font-size: 16px;
}

.file-details {
  flex: 1;
}

.file-name {
  font-weight: 500;
  color: #303133;
  margin-bottom: 2px;
}

.file-size {
  font-size: 12px;
  color: #909399;
}

.progress-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.progress-text {
  font-size: 12px;
  color: #606266;
  min-width: 40px;
}

.time-cell {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #606266;
}

.action-buttons {
  display: flex;
  justify-content: space-evenly;
  align-items: center;
  padding: 0 4px;
  min-width: fit-content;
  width: 100%;
}

.action-buttons .el-button {
  flex: 1;
  margin: 0 2px;
  min-width: 50px;
  max-width: 70px;
}

.session-details {
  margin-bottom: 20px;
}

.dialog-footer {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

:deep(.el-progress-bar) {
  flex: 1;
}

:deep(.el-progress--small) {
  font-size: 12px;
}
</style>
