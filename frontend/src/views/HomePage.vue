<template>
  <div class="home-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <h1 class="page-title">{{ getGreeting() }}，{{ userName }}！</h1>
          <p class="page-subtitle">
            {{ getRoleDescription() }}
          </p>
        </div>
        <div class="header-right">
          <el-button :loading="isRefreshing" circle @click="refreshDashboard">
            <el-icon><Refresh /></el-icon>
          </el-button>
        </div>
      </div>
    </div>

    <!-- 主要内容 -->
    <div class="page-content">
      <!-- 统计卡片 -->
      <div class="stats-section">
        <div class="stats-grid">
          <div
            v-for="card in statsCards"
            :key="card.title"
            class="stat-card"
            :class="`stat-card--${card.color}`"
          >
            <div class="stat-card__icon">
              <el-icon :size="32">
                <component :is="card.icon" />
              </el-icon>
            </div>
            <div class="stat-card__content">
              <div class="stat-card__value">{{ card.value }}</div>
              <div class="stat-card__title">{{ card.title }}</div>
              <div v-if="card.trend" class="stat-card__trend">
                {{ card.trend }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 主要内容区域 -->
      <div class="main-content">
        <!-- 左侧内容 -->
        <div class="content-left">
          <!-- 快捷操作 -->
          <div v-if="processedQuickActions && processedQuickActions.length > 0" class="section-card">
            <div class="section-header">
              <h3 class="section-title">快捷操作</h3>
            </div>
            <div
              :key="`quick-actions-${userStore.user?.id}-${userStore.user?.role}`"
              class="quick-actions"
            >
              <div
                v-for="action in processedQuickActions"
                :key="`${action.key}-${userStore.user?.id}`"
                class="quick-action"
                @click="handleQuickAction(action)"
              >
                <div
                  class="action-icon"
                  :class="`action-icon--${action.color}`"
                >
                  <el-icon :size="24">
                    <component :is="action.icon" />
                  </el-icon>
                </div>
                <div class="action-content">
                  <div class="action-title">{{ action.title }}</div>
                  <div class="action-description">{{ action.description }}</div>
                </div>
                <div class="action-arrow">
                  <el-icon><ArrowRight /></el-icon>
                </div>
              </div>
            </div>
          </div>

          <!-- 最近活动 -->
          <div class="section-card">
            <div class="section-header">
              <h3 class="section-title">最近活动</h3>
              <el-button text size="small" @click="viewAllActivities">
                查看全部
              </el-button>
            </div>
            <div class="activities">
              <div
                v-for="activity in recentActivities"
                :key="activity.id"
                class="activity-item"
                @click="handleActivityClick(activity)"
              >
                <div class="activity-icon">
                  <el-icon :size="16">
                    <component :is="getActivityIcon(activity.type)" />
                  </el-icon>
                </div>
                <div class="activity-content">
                  <div class="activity-title">{{ activity.title }}</div>
                  <div class="activity-description">
                    {{ activity.description }}
                  </div>
                  <div class="activity-time">
                    {{ formatTime(activity.time) }}
                  </div>
                </div>
                <div class="activity-status">
                  <el-tag :type="getStatusType(activity.status)" size="small">
                    {{ formatStatus(activity.status) }}
                  </el-tag>
                </div>
              </div>

              <div v-if="recentActivities.length === 0" class="empty-state">
                <el-icon :size="48" class="empty-icon">
                  <DocumentCopy />
                </el-icon>
                <p class="empty-text">暂无活动记录</p>
              </div>
            </div>
          </div>
        </div>

        <!-- 右侧内容 -->
        <div class="content-right">
          <!-- 通知信息 -->
          <div class="section-card">
            <div class="section-header">
              <h3 class="section-title">
                通知消息
                <el-badge
                  v-if="hasUnreadNotifications"
                  :value="unreadNotificationCount"
                  class="notification-badge"
                />
              </h3>
              <el-button
                v-if="hasUnreadNotifications"
                text
                size="small"
                @click="markAllNotificationsRead"
              >
                全部已读
              </el-button>
            </div>
            <div class="notifications">
              <div
                v-for="notification in notifications"
                :key="notification.id"
                class="notification-item"
                :class="{ 'notification-item--unread': !notification.is_read }"
                @click="handleNotificationClick(notification)"
              >
                <div
                  class="notification-icon"
                  :class="`notification-icon--${notification.type}`"
                >
                  <el-icon :size="16">
                    <component :is="getNotificationIcon(notification.type)" />
                  </el-icon>
                </div>
                <div class="notification-content">
                  <div class="notification-title">{{ notification.title }}</div>
                  <div class="notification-message">
                    {{ notification.content }}
                  </div>
                  <div class="notification-time">
                    {{ formatTime(notification.created_at) }}
                  </div>
                </div>
              </div>

              <div v-if="notifications.length === 0" class="empty-state">
                <el-icon :size="48" class="empty-icon">
                  <Bell />
                </el-icon>
                <p class="empty-text">暂无通知消息</p>
              </div>
            </div>
          </div>

          <!-- 系统信息（管理员） -->
          <div v-if="userRole === 'admin'" class="section-card">
            <div class="section-header">
              <h3 class="section-title">系统信息</h3>
            </div>
            <div class="system-info">
              <div class="info-item">
                <span class="info-label">系统版本</span>
                <span class="info-value">{{
                  stats.system?.version || "v1.0.0"
                }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">运行时间</span>
                <span class="info-value">{{
                  stats.system?.uptime || "15天"
                }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">磁盘使用</span>
                <span class="info-value">{{
                  stats.system?.diskUsage || "45%"
                }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">最后备份</span>
                <span class="info-value">{{
                  stats.system?.lastBackup || "今天"
                }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import {
  computed,
  markRaw,
  onMounted,
  onUnmounted,
  watch,
  nextTick,
} from "vue";
import { useRouter } from "vue-router";
import {
  Refresh,
  ArrowRight,
  DocumentCopy,
  Bell,
  Document,
  Check,
  Setting,
  InfoFilled,
  WarningFilled,
  SuccessFilled,
  CircleCheckFilled,
  Timer,
  Loading,
  Upload,
  User,
  UserFilled,
  FolderOpened,
  DataBoard,
  EditPen,
  DocumentAdd,
  Management,
} from "@element-plus/icons-vue";

import { ElMessage } from "element-plus";
import { useUserStore } from "@/stores/user";
import { useNotificationStore } from "@/stores/notifications";
import { useDashboard } from "@/composables/useDashboard";
import { useTabs } from "@/composables/useTabs";
import { contractUtils } from "@/api/contracts";

const router = useRouter();
const { openTab } = useTabs();

// 认证和权限
const userStore = useUserStore();
const user = computed(() => userStore.user);
const userRole = computed(() => userStore.userRole);

// 通知管理
const notificationStore = useNotificationStore();

// 首页数据
const {
  stats,
  quickActions,
  recentActivities,
  isRefreshing,
  refreshDashboard,
  getStatsCards,
  getQuickActions,
} = useDashboard();

// 通知相关计算属性
const notifications = computed(() =>
  notificationStore.notifications.slice(0, 5),
); // 只显示最新5条
const unreadNotificationCount = computed(() => notificationStore.unreadCount);
const hasUnreadNotifications = computed(
  () => notificationStore.hasUnreadNotifications,
);

// 用户名
const userName = computed(() => {
  if (!userStore.user) return "未登录";
  return userStore.user.username || "用户";
});

// 快捷操作图标映射
const quickActionIconMap = {
  Timer: markRaw(Timer),
  Loading: markRaw(Loading),
  Check: markRaw(Check),
  Upload: markRaw(Upload),
  Document: markRaw(DocumentCopy),
  User: markRaw(User),
  UserFilled: markRaw(UserFilled),
  FolderOpened: markRaw(FolderOpened),
  DataBoard: markRaw(DataBoard),
  DocumentAdd: markRaw(DocumentAdd),
  Management: markRaw(Management),
  EditPen: markRaw(EditPen),
};

// 处理快捷操作图标
const processQuickActions = (actions) => {
  return actions.map((action) => ({
    ...action,
    icon: quickActionIconMap[action.icon] || markRaw(DocumentCopy),
  }));
};

// 统计卡片
const statsCards = computed(() => getStatsCards());

// 处理后的快捷操作（带图标映射）
const processedQuickActions = computed(() => {
  return processQuickActions(quickActions.value || []);
});

// 监听用户变化，强制刷新快捷操作
watch(
  () => userStore.user?.id,
  async (newUserId, oldUserId) => {
    if (newUserId && newUserId !== oldUserId) {
      // 强制重新获取快捷操作数据
      await nextTick();
      await getQuickActions();
    }
  },
  { immediate: false },
);

// 获取问候语
const getGreeting = () => {
  const hour = new Date().getHours();
  if (hour < 12) return "早上好";
  if (hour < 18) return "下午好";
  return "晚上好";
};

// 获取角色描述
const getRoleDescription = () => {
  const descriptions = {
    employee: "欢迎使用合同审核系统，您可以提交合同并查看审核状态",
    reviewer: "您有新的合同需要审核，请及时处理",
    admin: "系统运行正常，您可以管理用户和查看统计信息",
  };
  return descriptions[userRole.value] || "欢迎使用合同审核系统";
};

// 处理快捷操作
const handleQuickAction = (action) => {
  if (action.path) {
    // 使用Tab系统打开页面，避免路由跳转导致的页面刷新
    const tabConfig = getTabConfigByPath(action.path);
    if (tabConfig) {
      openTab(tabConfig);
    }
  }
};

// 查看所有活动
const viewAllActivities = () => {
  // 根据角色打开相应的Tab页面
  const tabConfigs = {
    employee: {
      key: "my-contracts",
      title: "我的合同",
      path: "/my-contracts",
      component: "MyContractsPage",
      icon: "Document",
    },
    reviewer: {
      key: "review-management",
      title: "审核管理",
      path: "/review-management",
      component: "ReviewManagePage",
      icon: "FolderOpened",
    },
    admin: {
      key: "contract-management",
      title: "合同管理",
      path: "/contract-management",
      component: "ContractManagePage",
      icon: "FolderOpened",
    },
    legal_officer: {
      key: "contract-management",
      title: "合同编号管理",
      path: "/contract-management",
      component: "ContractManagePage",
      icon: "Document",
    },
  };
  const tabConfig = tabConfigs[userRole.value];
  if (tabConfig) {
    openTab(tabConfig);
  }
};

// 处理活动点击
const handleActivityClick = (activity) => {
  try {
    // 统一处理：所有活动都应该有合同ID，直接跳转到合同详情
    if (activity.id || activity.related_id) {
      const contractId = activity.id || activity.related_id;
      // 跳转到合同详情
      openTab({
        key: `contract-detail-${contractId}`,
        title: `合同详情`,
        path: `/contracts/${contractId}`,
        component: "ContractDetailTab",
        icon: "Document",
        params: { contractId: contractId },
      });
    } else {
      // 如果没有合同ID，显示提示信息
      ElMessage.warning("无法获取合同详情信息");
    }
  } catch (error) {
    console.error("处理活动点击失败:", error);
    ElMessage.error("操作失败");
  }
};

// 根据路径获取Tab配置
const getTabConfigByPath = (path) => {
  const pathToTabMap = {
    "/submit": {
      key: "submit",
      title: "提交合同",
      path: "/submit",
      component: "SubmitPage",
      icon: "Upload",
    },
    "/my-contracts": {
      key: "my-contracts",
      title: "我的合同",
      path: "/my-contracts",
      component: "MyContractsPage",
      icon: "Document",
    },
    "/contract-management": {
      key: "contract-management",
      title: "合同管理",
      path: "/contract-management",
      component: "ContractManagePage",
      icon: "FolderOpened",
    },
    "/user-management": {
      key: "user-management",
      title: "用户管理",
      path: "/user-management",
      component: "UserManagePage",
      icon: "UserFilled",
    },
    "/system-stats": {
      key: "system-stats",
      title: "系统统计",
      path: "/system-stats",
      component: "SystemManagePage",
      icon: "DataBoard",
    },
    "/settings": {
      key: "settings",
      title: "系统设置",
      path: "/settings",
      component: "SettingsPage",
      icon: "Setting",
    },
    "/profile": {
      key: "profile",
      title: "个人资料",
      path: "/profile",
      component: "ProfilePage",
      icon: "User",
    },
    // 审核员的具体页面
    "/pending-review": {
      key: "review-management",
      title: "审核管理",
      path: "/review-management",
      component: "ReviewManagePage",
      icon: "Clock",
      params: { defaultTab: "pending" },
    },

    "/reviewed": {
      key: "review-management",
      title: "审核管理",
      path: "/review-management",
      component: "ReviewManagePage",
      icon: "Check",
      params: { defaultTab: "reviewed" },
    },
    // 审核员统一的审核管理页面
    "/review-management": {
      key: "review-management",
      title: "审核管理",
      path: "/review-management",
      component: "ReviewManagePage",
      icon: "FolderOpened",
    },
    "/statistics": {
      key: "statistics",
      title: "审核统计",
      path: "/statistics",
      component: "StatisticsPage",
      icon: "DataAnalysis",
    },
    "/contract-statistics": {
      key: "contract-statistics",
      title: "合同统计",
      path: "/contract-statistics",
      component: "ContractStatisticsPage",
      icon: "DataAnalysis",
    },
  };
  return pathToTabMap[path];
};

// 处理通知点击
const handleNotificationClick = async (notification) => {
  try {
    // 如果是未读通知，标记为已读
    if (!notification.is_read) {
      await notificationStore.markAsRead(notification.id);
    }

    // 根据通知类型进行相应的跳转
    if (notification.related_type === "contract" && notification.related_id) {
      // 跳转到合同详情
      openTab({
        key: `contract-detail-${notification.related_id}`,
        title: `合同详情`,
        path: `/contracts/${notification.related_id}`,
        component: "ContractDetailTab",
        icon: "Document",
        params: { contractId: notification.related_id },
      });
    }
  } catch (error) {
    console.error("处理通知点击失败:", error);
    ElMessage.error("操作失败");
  }
};

// 标记所有通知为已读
const markAllNotificationsRead = async () => {
  try {
    await notificationStore.markAllAsRead();
    ElMessage.success("所有通知已标记为已读");
  } catch (error) {
    console.error("标记所有通知已读失败:", error);
    ElMessage.error("操作失败");
  }
};

// 获取活动图标
const getActivityIcon = (type) => {
  const icons = {
    contract: markRaw(Document),
    review: markRaw(Check),
    system: markRaw(Setting),
  };
  return icons[type] || markRaw(Document);
};

// 获取通知图标
const getNotificationIcon = (type) => {
  const icons = {
    info: markRaw(InfoFilled),
    warning: markRaw(WarningFilled),
    success: markRaw(SuccessFilled),
    error: markRaw(CircleCheckFilled),
  };
  return icons[type] || markRaw(InfoFilled);
};

// 获取状态类型
const getStatusType = (status) => {
  const types = {
    pending: "warning",
    pending_city_review: "warning",
    pending_contract_number: "info",
    approved: "success",
    completed: "success",
    rejected: "danger",
  };
  return types[status] || "info";
};

// 格式化状态
const formatStatus = contractUtils.formatStatus;

// 格式化时间
const formatTime = (time) => {
  return contractUtils.getRelativeTime(time);
};

// 组件挂载时初始化数据
onMounted(async () => {
  try {
    // 检查用户是否已认证且不在登出过程中
    if (!userStore.isAuthenticated || window.isLoggingOut) {
      return;
    }

    // 初始化通知数据
    await notificationStore.fetchNotifications({ page: 1, pageSize: 5 });
  } catch (error) {
    console.error("初始化首页通知数据失败:", error);
  }
});

// 组件卸载时清理
onUnmounted(() => {
  try {
    // 重置通知状态，避免在登出时继续请求
    notificationStore.reset();
  } catch (error) {
    console.warn("❌ HomePage 组件清理失败:", error);
  }
});
</script>

<style scoped>
.home-page {
  min-height: 100%;
  background: #f5f5f5;
}

.page-header {
  background: #fff;
  border-bottom: 1px solid #e4e7ed;
  padding: 24px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 8px;
}

.page-subtitle {
  font-size: 14px;
  color: #909399;
  margin: 0;
}

.page-content {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.stats-section {
  margin-bottom: 24px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  gap: 20px;
  justify-content: center;
  max-width: 1200px;
  margin: 0 auto;
}

.stat-card {
  background: #fff;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 16px;
  transition: transform 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
}

.stat-card__icon {
  width: 64px;
  height: 64px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
}

.stat-card--primary .stat-card__icon {
  background: linear-gradient(135deg, #409eff, #337ecc);
}

.stat-card--success .stat-card__icon {
  background: linear-gradient(135deg, #67c23a, #529b2e);
}

.stat-card--warning .stat-card__icon {
  background: linear-gradient(135deg, #e6a23c, #b88230);
}

.stat-card--danger .stat-card__icon {
  background: linear-gradient(135deg, #f56c6c, #c45656);
}

.stat-card--info .stat-card__icon {
  background: linear-gradient(135deg, #909399, #73767a);
}

.stat-card__value {
  font-size: 28px;
  font-weight: 700;
  color: #303133;
  line-height: 1;
}

.stat-card__title {
  font-size: 14px;
  color: #606266;
  margin-top: 4px;
}

.stat-card__trend {
  font-size: 12px;
  color: #67c23a;
  margin-top: 4px;
}

.main-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.section-card {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 24px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #f0f0f0;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.quick-actions {
  padding: 8px;
}

.quick-action {
  display: flex;
  align-items: center;
  padding: 16px;
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.quick-action:hover {
  background-color: #f8f9fa;
}

.action-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  margin-right: 16px;
}

.action-icon--primary {
  background: linear-gradient(135deg, #409eff, #337ecc);
}

.action-icon--success {
  background: linear-gradient(135deg, #67c23a, #529b2e);
}

.action-icon--warning {
  background: linear-gradient(135deg, #e6a23c, #b88230);
}

.action-icon--danger {
  background: linear-gradient(135deg, #f56c6c, #c45656);
}

.action-icon--info {
  background: linear-gradient(135deg, #909399, #73767a);
}

.action-content {
  flex: 1;
}

.action-title {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.action-description {
  font-size: 12px;
  color: #909399;
}

.action-arrow {
  color: #c0c4cc;
}

.activities,
.notifications {
  padding: 8px;
  max-height: 400px;
  overflow-y: auto;
}

.activity-item,
.notification-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-radius: 6px;
  margin-bottom: 8px;
  transition: background-color 0.3s ease;
}

.activity-item:hover,
.notification-item:hover {
  background-color: #f8f9fa;
}

.notification-item--unread {
  background-color: #ecf5ff;
  border-left: 3px solid #409eff;
}

.activity-icon,
.notification-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  background-color: #f0f0f0;
  color: #606266;
}

.notification-icon--info {
  background-color: #ecf5ff;
  color: #409eff;
}

.notification-icon--warning {
  background-color: #fdf6ec;
  color: #e6a23c;
}

.notification-icon--success {
  background-color: #f0f9ff;
  color: #67c23a;
}

.notification-icon--error {
  background-color: #fef0f0;
  color: #f56c6c;
}

.activity-content,
.notification-content {
  flex: 1;
  min-width: 0;
}

.activity-title,
.notification-title {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.activity-description,
.notification-message {
  font-size: 12px;
  color: #909399;
  margin-bottom: 4px;
}

.activity-time,
.notification-time {
  font-size: 11px;
  color: #c0c4cc;
}

.activity-status {
  margin-left: 12px;
}

.system-info {
  padding: 16px 24px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  font-size: 14px;
  color: #606266;
}

.info-value {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
}

.empty-state {
  text-align: center;
  padding: 40px 20px;
}

.empty-icon {
  color: #c0c4cc;
  margin-bottom: 16px;
}

.empty-text {
  font-size: 14px;
  color: #909399;
  margin: 0;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .main-content {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .page-content {
    padding: 16px;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .stat-card {
    padding: 20px;
  }

  .section-header {
    padding: 16px 20px;
  }

  .quick-action {
    padding: 12px;
  }

  .action-icon {
    width: 40px;
    height: 40px;
    margin-right: 12px;
  }
}
</style>
