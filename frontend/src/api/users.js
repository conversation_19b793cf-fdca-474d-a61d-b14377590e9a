/**
 * 用户管理 API
 * 提供用户相关的接口调用
 */

import api from "./request";

/**
 * 用户 API 类
 */
export const usersAPI = {
  /**
   * 获取用户列表
   * @param {Object} params - 查询参数
   * @returns {Promise} 用户列表
   */
  getList(params = {}) {
    return api.get("/users", { params });
  },

  /**
   * 获取用户详情
   * @param {number} id - 用户ID
   * @returns {Promise} 用户详情
   */
  getDetail(id) {
    return api.get(`/users/${id}`);
  },

  /**
   * 创建用户
   * @param {Object} userData - 用户数据
   * @returns {Promise} 创建结果
   */
  create(userData) {
    return api.post("/users", userData);
  },

  /**
   * 更新用户
   * @param {number} id - 用户ID
   * @param {Object} userData - 用户数据
   * @returns {Promise} 更新结果
   */
  update(id, userData) {
    return api.put(`/users/${id}`, userData);
  },

  /**
   * 删除用户
   * @param {number} id - 用户ID
   * @returns {Promise} 删除结果
   */
  delete(id) {
    return api.delete(`/users/${id}`);
  },

  /**
   * 重置用户密码
   * @param {number} id - 用户ID
   * @param {string} newPassword - 新密码
   * @returns {Promise} 重置结果
   */
  resetPassword(id, newPassword) {
    return api.post(`/users/${id}/reset-password`, { newPassword });
  },
};

/**
 * 管理员 API 类
 */
export const adminAPI = {
  /**
   * 获取用户列表（管理员专用）
   * @param {Object} params - 查询参数
   * @returns {Promise} 用户列表
   */
  getUsers(params = {}) {
    return api.get("/admin/users", { params });
  },

  /**
   * 创建用户（管理员专用）
   * @param {Object} userData - 用户数据
   * @returns {Promise} 创建结果
   */
  createUser(userData) {
    return api.post("/admin/users", userData);
  },

  /**
   * 更新用户（管理员专用）
   * @param {number} id - 用户ID
   * @param {Object} userData - 用户数据
   * @returns {Promise} 更新结果
   */
  updateUser(id, userData) {
    return api.put(`/admin/users/${id}`, userData);
  },

  /**
   * 删除用户（管理员专用）
   * @param {number} id - 用户ID
   * @returns {Promise} 删除结果
   */
  deleteUser(id) {
    return api.delete(`/admin/users/${id}`);
  },

  /**
   * 重置用户密码（管理员专用）
   * @param {number} id - 用户ID
   * @param {Object} passwordData - 密码数据
   * @returns {Promise} 重置结果
   */
  resetUserPassword(id, passwordData) {
    return api.put(`/admin/users/${id}/reset-password`, passwordData);
  },

  /**
   * 获取系统统计信息
   * @returns {Promise} 系统统计
   */
  getSystemStats() {
    return api.get("/admin/system-stats");
  },

  /**
   * 获取系统日志
   * @param {Object} params - 查询参数
   * @returns {Promise} 系统日志
   */
  getSystemLogs(params = {}) {
    return api.get("/admin/system-logs", { params });
  },

  /**
   * 导出用户数据
   * @param {Object} params - 导出参数
   * @returns {Promise} 导出结果
   */
  exportUsers(params = {}) {
    return api.get("/admin/export/users", {
      params,
      responseType: "blob",
    });
  },

  /**
   * 导出合同数据
   * @param {Object} params - 导出参数
   * @returns {Promise} 导出结果
   */
  exportContracts(params = {}) {
    return api.get("/admin/export/contracts", {
      params,
      responseType: "blob",
    });
  },

  /**
   * 系统备份
   * @returns {Promise} 备份结果
   */
  backup() {
    return api.post("/admin/backup", {}, {
      responseType: "blob",
    });
  },

  /**
   * 系统还原
   * @param {File} backupFile - 备份文件
   * @returns {Promise} 还原结果
   */
  restore(backupFile) {
    const formData = new FormData();
    formData.append("backup", backupFile);

    return api.post("/admin/restore", formData, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
  },
};

/**
 * 用户工具函数
 */
export const userUtils = {
  /**
   * 格式化用户角色
   * @param {string} role - 角色值
   * @returns {string} 角色文本
   */
  formatRole(role) {
    const roleMap = {
      employee: "员工",
      county_reviewer: "县级审核员",
      city_reviewer: "市级审核员",
      legal_officer: "市局法规员",
      admin: "管理员",
    };
    return roleMap[role] || role;
  },

  /**
   * 获取角色颜色
   * @param {string} role - 角色值
   * @returns {string} 颜色类型
   */
  getRoleColor(role) {
    const colorMap = {
      employee: "info",
      county_reviewer: "warning",
      city_reviewer: "warning",
      legal_officer: "success",
      admin: "danger",
    };
    return colorMap[role] || "info";
  },

  /**
   * 格式化用户状态
   * @param {string} status - 状态值
   * @returns {string} 状态文本
   */
  formatStatus(status) {
    const statusMap = {
      active: "正常",
      inactive: "禁用",
      banned: "封禁",
    };
    return statusMap[status] || status;
  },

  /**
   * 获取状态颜色
   * @param {string} status - 状态值
   * @returns {string} 颜色类型
   */
  getStatusColor(status) {
    const colorMap = {
      active: "success",
      inactive: "warning",
      banned: "danger",
    };
    return colorMap[status] || "info";
  },

  /**
   * 检查用户权限
   * @param {Object} user - 用户对象
   * @param {string} permission - 权限名称
   * @returns {boolean} 是否有权限
   */
  hasPermission(user, permission) {
    if (!user || !user.role) return false;

    const permissions = {
      employee: ["contract:create", "contract:read", "contract:update"],
      county_reviewer: ["contract:read", "contract:review"],
      city_reviewer: ["contract:read", "contract:review"],
      legal_officer: ["contract:read", "contract:assign_number"],
      admin: ["*"], // 管理员拥有所有权限
    };

    const userPermissions = permissions[user.role] || [];
    return (
      userPermissions.includes("*") || userPermissions.includes(permission)
    );
  },

  /**
   * 检查是否可以操作用户
   * @param {Object} currentUser - 当前用户
   * @param {Object} targetUser - 目标用户
   * @param {string} action - 操作类型
   * @returns {boolean} 是否可以操作
   */
  canOperateUser(currentUser, targetUser, action) {
    if (!currentUser || !targetUser) return false;

    // 只有管理员可以操作用户
    if (currentUser.role !== "admin") return false;

    // 不能操作自己（除了查看）
    if (currentUser.id === targetUser.id && action !== "view") return false;

    return true;
  },

  /**
   * 生成随机密码
   * @param {number} length - 密码长度
   * @returns {string} 随机密码
   */
  generatePassword(length = 8) {
    const chars =
      "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
    let password = "";

    for (let i = 0; i < length; i++) {
      password += chars.charAt(Math.floor(Math.random() * chars.length));
    }

    return password;
  },

  /**
   * 验证用户名格式
   * @param {string} username - 用户名
   * @returns {boolean} 是否有效
   */
  validateUsername(username) {
    if (!username) return false;

    // 3-50个字符，只能包含字母、数字和下划线
    const regex = /^[a-zA-Z0-9_]{3,50}$/;
    return regex.test(username);
  },

  /**
   * 验证密码强度
   * @param {string} password - 密码
   * @returns {Object} 验证结果
   */
  validatePassword(password) {
    const result = {
      valid: false,
      strength: "weak",
      messages: [],
    };

    if (!password) {
      result.messages.push("密码不能为空");
      return result;
    }

    if (password.length < 6) {
      result.messages.push("密码长度至少6个字符");
      return result;
    }

    let score = 0;

    // 长度检查
    if (password.length >= 8) score += 1;
    if (password.length >= 12) score += 1;

    // 复杂度检查
    if (/[a-z]/.test(password)) score += 1;
    if (/[A-Z]/.test(password)) score += 1;
    if (/[0-9]/.test(password)) score += 1;
    if (/[^a-zA-Z0-9]/.test(password)) score += 1;

    result.valid = true;

    if (score < 3) {
      result.strength = "weak";
      result.messages.push("密码强度较弱，建议包含大小写字母、数字和特殊字符");
    } else if (score < 5) {
      result.strength = "medium";
    } else {
      result.strength = "strong";
    }

    return result;
  },
};

export default usersAPI;
