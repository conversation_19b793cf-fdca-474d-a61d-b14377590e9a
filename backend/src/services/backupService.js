/**
 * 数据备份服务
 * 支持数据库和文件的备份与还原
 */

const fs = require('fs');
const path = require('path');
const archiver = require('archiver');
const yauzl = require('yauzl');
const { DateTimeUtils } = require('../utils/helpers');

class BackupService {
  constructor() {
    this.dbPath = path.join(__dirname, '../../database.db');
    this.uploadsPath = path.join(__dirname, '../../uploads');
    this.tempPath = path.join(__dirname, '../../temp');
    
    // 确保临时目录存在
    if (!fs.existsSync(this.tempPath)) {
      fs.mkdirSync(this.tempPath, { recursive: true });
    }
  }

  /**
   * 创建系统备份
   * @returns {Promise<Buffer>} 备份文件的Buffer
   */
  async createBackup() {
    return new Promise((resolve, reject) => {
      try {
        // 创建内存流来存储压缩包
        const chunks = [];
        const archive = archiver('zip', {
          zlib: { level: 9 } // 最高压缩级别
        });

        // 监听数据事件
        archive.on('data', (chunk) => {
          chunks.push(chunk);
        });

        // 监听结束事件
        archive.on('end', () => {
          const buffer = Buffer.concat(chunks);
          resolve(buffer);
        });

        // 监听错误事件
        archive.on('error', (err) => {
          reject(err);
        });

        // 添加数据库文件
        if (fs.existsSync(this.dbPath)) {
          archive.file(this.dbPath, { name: 'database.db' });
        } else {
          throw new Error('数据库文件不存在');
        }

        // 添加上传文件目录
        if (fs.existsSync(this.uploadsPath)) {
          archive.directory(this.uploadsPath, 'uploads');
        }

        // 添加备份信息文件
        const backupInfo = {
          version: '1.0.0',
          timestamp: DateTimeUtils.nowBeijing(),
          description: '系统数据备份',
          includes: {
            database: true,
            uploads: fs.existsSync(this.uploadsPath)
          }
        };

        archive.append(JSON.stringify(backupInfo, null, 2), { name: 'backup-info.json' });

        // 完成压缩
        archive.finalize();

      } catch (error) {
        reject(error);
      }
    });
  }

  /**
   * 还原系统备份
   * @param {Buffer} backupBuffer - 备份文件的Buffer
   * @returns {Promise<Object>} 还原结果
   */
  async restoreBackup(backupBuffer) {
    return new Promise((resolve, reject) => {
      try {
        // 创建临时文件
        const tempBackupPath = path.join(this.tempPath, `restore_${Date.now()}.zip`);
        fs.writeFileSync(tempBackupPath, backupBuffer);

        // 解压备份文件
        yauzl.open(tempBackupPath, { lazyEntries: true }, (err, zipfile) => {
          if (err) {
            this.cleanupTempFile(tempBackupPath);
            return reject(err);
          }

          const extractedFiles = [];
          let backupInfo = null;

          zipfile.readEntry();

          zipfile.on('entry', (entry) => {
            if (/\/$/.test(entry.fileName)) {
              // 目录条目
              zipfile.readEntry();
            } else {
              // 文件条目
              zipfile.openReadStream(entry, (err, readStream) => {
                if (err) {
                  this.cleanupTempFile(tempBackupPath);
                  return reject(err);
                }

                const chunks = [];
                readStream.on('data', (chunk) => {
                  chunks.push(chunk);
                });

                readStream.on('end', () => {
                  const fileBuffer = Buffer.concat(chunks);
                  extractedFiles.push({
                    fileName: entry.fileName,
                    buffer: fileBuffer
                  });

                  // 如果是备份信息文件，解析它
                  if (entry.fileName === 'backup-info.json') {
                    try {
                      backupInfo = JSON.parse(fileBuffer.toString());
                    } catch (parseErr) {
                      console.warn('解析备份信息失败:', parseErr);
                    }
                  }

                  zipfile.readEntry();
                });
              });
            }
          });

          zipfile.on('end', async () => {
            try {
              // 执行还原操作
              await this.performRestore(extractedFiles, backupInfo);
              
              // 清理临时文件
              this.cleanupTempFile(tempBackupPath);
              
              resolve({
                success: true,
                message: '数据还原成功',
                backupInfo,
                restoredFiles: extractedFiles.length
              });
            } catch (restoreErr) {
              this.cleanupTempFile(tempBackupPath);
              reject(restoreErr);
            }
          });
        });

      } catch (error) {
        reject(error);
      }
    });
  }

  /**
   * 执行还原操作
   * @param {Array} extractedFiles - 解压的文件列表
   * @param {Object} backupInfo - 备份信息
   */
  async performRestore(extractedFiles, backupInfo) {
    // 创建当前数据的备份（以防还原失败）
    const currentBackupPath = path.join(this.tempPath, `current_backup_${Date.now()}.db`);
    if (fs.existsSync(this.dbPath)) {
      fs.copyFileSync(this.dbPath, currentBackupPath);
    }

    try {
      // 还原数据库
      const dbFile = extractedFiles.find(f => f.fileName === 'database.db');
      if (dbFile) {
        fs.writeFileSync(this.dbPath, dbFile.buffer);
      }

      // 还原上传文件
      const uploadFiles = extractedFiles.filter(f => f.fileName.startsWith('uploads/'));
      if (uploadFiles.length > 0) {
        // 清空现有上传目录
        if (fs.existsSync(this.uploadsPath)) {
          fs.rmSync(this.uploadsPath, { recursive: true, force: true });
        }
        fs.mkdirSync(this.uploadsPath, { recursive: true });

        // 还原文件
        for (const file of uploadFiles) {
          const filePath = path.join(__dirname, '../../', file.fileName);
          const fileDir = path.dirname(filePath);
          
          // 确保目录存在
          if (!fs.existsSync(fileDir)) {
            fs.mkdirSync(fileDir, { recursive: true });
          }
          
          fs.writeFileSync(filePath, file.buffer);
        }
      }

      // 清理当前备份文件（还原成功）
      if (fs.existsSync(currentBackupPath)) {
        fs.unlinkSync(currentBackupPath);
      }

    } catch (error) {
      // 还原失败，恢复原始数据库
      if (fs.existsSync(currentBackupPath)) {
        fs.copyFileSync(currentBackupPath, this.dbPath);
        fs.unlinkSync(currentBackupPath);
      }
      throw error;
    }
  }

  /**
   * 清理临时文件
   * @param {string} filePath - 文件路径
   */
  cleanupTempFile(filePath) {
    try {
      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
      }
    } catch (error) {
      console.warn('清理临时文件失败:', error);
    }
  }

  /**
   * 生成备份文件名
   * @returns {string} 备份文件名
   */
  generateBackupFileName() {
    const timestamp = DateTimeUtils.nowBeijing().replace(/[:\s]/g, '-');
    return `system-backup-${timestamp}.zip`;
  }

  /**
   * 获取系统信息用于备份
   * @returns {Object} 系统信息
   */
  getSystemInfo() {
    const stats = {
      database: {
        exists: fs.existsSync(this.dbPath),
        size: fs.existsSync(this.dbPath) ? fs.statSync(this.dbPath).size : 0
      },
      uploads: {
        exists: fs.existsSync(this.uploadsPath),
        fileCount: 0,
        totalSize: 0
      }
    };

    // 统计上传文件
    if (stats.uploads.exists) {
      try {
        const files = this.getAllFiles(this.uploadsPath);
        stats.uploads.fileCount = files.length;
        stats.uploads.totalSize = files.reduce((total, file) => {
          return total + fs.statSync(file).size;
        }, 0);
      } catch (error) {
        console.warn('统计上传文件失败:', error);
      }
    }

    return stats;
  }

  /**
   * 递归获取目录下所有文件
   * @param {string} dirPath - 目录路径
   * @returns {Array} 文件路径列表
   */
  getAllFiles(dirPath) {
    const files = [];
    
    function traverse(currentPath) {
      const items = fs.readdirSync(currentPath);
      
      for (const item of items) {
        const itemPath = path.join(currentPath, item);
        const stat = fs.statSync(itemPath);
        
        if (stat.isDirectory()) {
          traverse(itemPath);
        } else {
          files.push(itemPath);
        }
      }
    }
    
    traverse(dirPath);
    return files;
  }
}

module.exports = BackupService;
