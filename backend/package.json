{"name": "hetong-backend", "version": "1.0.0", "description": "合同审核系统后端 API", "main": "src/app.js", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js", "init-db": "node init-db.js", "lint": "eslint . --ext .js --fix", "lint:check": "eslint . --ext .js"}, "keywords": ["contract", "review", "system", "express", "sqlite"], "author": "Contract Review System", "license": "MIT", "dependencies": {"archiver": "^7.0.1", "axios": "^1.10.0", "bcrypt": "^5.1.1", "cors": "^2.8.5", "cropperjs": "^2.0.0", "dayjs": "^1.11.13", "dotenv": "^16.3.1", "exceljs": "^4.4.0", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "jspdf": "^3.0.1", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "pdf-lib": "^1.17.1", "redis": "^5.6.1", "sharp": "^0.34.3", "sqlite3": "^5.1.6", "ws": "^8.18.3", "yauzl": "^3.2.0"}, "devDependencies": {"nodemon": "^3.0.2"}, "engines": {"node": ">=18.0.0"}}